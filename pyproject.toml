[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pyleet"
version = "0.2.0"
description = "Run and test your LeetCode Python solutions locally"
readme = "README.md"
requires-python = ">=3.7"
authors = [
    {name = "ergs0204", email = "<EMAIL>"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[project.urls]
Homepage = "https://github.com/ergs0204/pyleet"

[project.scripts]
pyleet = "pyleet.__main__:main"

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]